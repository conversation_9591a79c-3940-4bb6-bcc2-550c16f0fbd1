[{"_id": "6290a500cc41b488ef6b07f8", "profileName": "ASP User Admin", "menuItems": [{"menuTitle": "Admin", "menuRoute": "", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "User Admin", "menuRoute": "user-admin/list", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "ASP Search", "menuRoute": "app/asp-customer/list", "roles": ["SEARCH_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "ASP Data Extract", "menuRoute": "app/asp-customer/extract", "roles": ["EXTRACT_CSV"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Rollup Report", "menuRoute": "app/asp-customer/rollup-rpt", "roles": ["ROLLUP_REPORT"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["SysAdmin", "EDIT_TEMPLATE", "SEARCH_ASP_CUSTOMER", "SEND_MESSAGES_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "EXTRACT_CSV", "ADD_ASP_CUSTOMER"], "menuBehavior": "Open", "defaultRoute": "", "createdAt": "2025-05-08T08:43:02.893Z", "updatedAt": "2025-05-08T08:43:02.893Z", "__v": 0}, {"_id": "6290a500cc41b488ef6b07f7", "profileName": "Past Due Admin", "menuItems": [{"menuTitle": "Past Due Data Extract", "menuRoute": "app/past-due/extract", "roles": ["PAST_DUE_EXTRACT_CSV"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["SysAdmin", "PAST_DUE_EXTRACT_CSV"], "menuBehavior": "Open", "defaultRoute": "", "createdAt": "2025-05-08T08:43:02.893Z", "updatedAt": "2025-05-08T08:43:02.893Z", "__v": 0}, {"_id": "6290a500cc41b488ef6b07f9", "profileName": "ASP_ADMIN", "menuItems": [{"menuTitle": "Admin", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "User Admin", "menuRoute": "user-admin/list", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Templates", "menuRoute": "app/template-admin/list", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "Search", "menuRoute": "app/asp-customer/list", "roles": ["SEARCH_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "ASP Data Extract", "menuRoute": "app/asp-customer/extract", "roles": ["EXTRACT_CSV"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["SysAdmin", "EDIT_TEMPLATE", "SEARCH_ASP_CUSTOMER", "SEND_MESSAGES_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "EXTRACT_CSV"], "menuBehavior": "Open", "defaultRoute": "", "createdAt": "2025-05-08T08:43:02.893Z", "updatedAt": "2025-05-08T08:43:02.893Z", "__v": 0}, {"_id": "6290a500cc41b488ef6b07fa", "profileName": "ASP_CSR", "menuItems": [{"menuTitle": "Search", "menuRoute": "app/asp-customer/list", "roles": ["SEARCH_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["SEARCH_ASP_CUSTOMER", "SEND_MESSAGES_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "ADD_ASP_CUSTOMER"], "menuBehavior": "Open", "defaultRoute": "", "createdAt": "2025-05-08T08:43:02.893Z", "updatedAt": "2025-05-08T08:43:02.893Z", "__v": 0}, {"_id": "62bdd98c085f82352bb9b631", "profileName": "Electric Operations", "menuItems": [{"menuTitle": "Electric Operations", "menuRoute": "app/eo/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/eo/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": true, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "createdAt": "2025-05-08T08:43:02.893Z", "updatedAt": "2025-05-08T08:43:02.893Z", "__v": 0}, {"_id": "62cf1e5111d300ed212025f6", "profileName": "Revenue Operations", "menuItems": [{"menuTitle": "Revenue Operations", "menuRoute": "app/rev/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/rev/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": true, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "createdAt": "2025-05-08T08:43:02.893Z", "updatedAt": "2025-05-08T08:43:02.893Z", "__v": 0}, {"_id": "6306b8ee2febeabdbd04498a", "profileName": "Customer Billing", "menuItems": [{"menuTitle": "Customer Billing", "menuRoute": "app/cbmm/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/cbmm/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "CSR Options", "menuRoute": "app/csr/edit/0", "menuItems": [], "expand": false, "elementRef": null, "roles": []}, {"menuTitle": "CSR Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": true, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "createdAt": "2025-05-08T08:43:02.894Z", "updatedAt": "2025-05-08T08:43:02.894Z", "__v": 0}, {"_id": "63fd600fc777b0fa9e9c66ca", "profileName": "Customer Care", "menuItems": [{"menuTitle": "Customer Care", "menuRoute": "app/cc/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Customer Care Search", "menuRoute": "app/cc/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "CSR Options", "menuRoute": "app/csr/edit/0", "menuItems": [], "expand": false, "elementRef": null, "roles": []}, {"menuTitle": "CSR Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": true, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "createdAt": "2025-05-08T08:43:02.894Z", "updatedAt": "2025-05-08T08:43:02.894Z", "__v": 0}, {"_id": "642b4f118e74fcb96b312a33", "profileName": "Business Center", "menuItems": [{"menuTitle": "Business Center", "menuRoute": "app/csr/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Business Center Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": true, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "createdAt": "2025-05-08T08:43:02.894Z", "updatedAt": "2025-05-08T08:43:02.894Z", "__v": 0}, {"_id": "6290a500cc41b488ef6b07fe", "profileName": "Admin", "menuItems": [{"menuTitle": "Admin", "menuRoute": "", "menuItems": [{"menuTitle": "Profile", "menuRoute": "profiles/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "User Admin", "menuRoute": "user-admin/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Agencies", "menuRoute": "app/agency/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Users Upload", "menuRoute": "app/user-upload/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Templates", "menuRoute": "app/template-admin/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}], "expand": true, "elementRef": null}, {"menuTitle": "CSR Options", "menuRoute": "app/csr/edit/0", "menuItems": [], "expand": false, "elementRef": null, "roles": []}, {"menuTitle": "Search", "menuRoute": "app/csr/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "CSV Extract", "menuRoute": "app/reports/csv-extract", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Summary report", "menuRoute": "app/reports/shutoff-hold-report", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["SysAdmin"], "menuBehavior": "Open", "defaultRoute": "", "createdAt": "2025-05-08T08:43:02.894Z", "updatedAt": "2025-05-08T08:43:02.894Z", "__v": 0}, {"_id": "6290a500cc41b488ef6b07fb", "profileName": "ASP Admin", "menuItems": [{"menuTitle": "Admin", "menuRoute": "", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "User Admin", "menuRoute": "user-admin/list", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Templates", "menuRoute": "app/template-admin/list", "roles": ["EDIT_TEMPLATE"], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "ASP Search", "menuRoute": "app/asp-customer/list", "roles": ["SEARCH_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "ASP Data Extract", "menuRoute": "app/asp-customer/extract", "roles": ["EXTRACT_CSV"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Rollup Report", "menuRoute": "app/asp-customer/rollup-rpt", "roles": ["ROLLUP_REPORT"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["SysAdmin", "EDIT_TEMPLATE", "SEARCH_ASP_CUSTOMER", "SEND_MESSAGES_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "EXTRACT_CSV", "ADD_ASP_CUSTOMER"], "menuBehavior": "Open", "defaultRoute": "", "createdAt": "2025-05-08T08:43:02.893Z", "updatedAt": "2025-05-08T08:43:02.893Z", "__v": 0}, {"_id": "6290a500cc41b488ef6b07fc", "profileName": "CSR", "menuItems": [{"menuTitle": "CSR Options", "menuRoute": "app/csr/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": true, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "createdAt": "2025-05-08T08:43:02.893Z", "updatedAt": "2025-05-08T08:43:02.893Z", "__v": 0}, {"_id": "62b118d6cd46f7dab4f59f59", "profileName": "Super Admin", "menuItems": [{"menuTitle": "Admin", "menuRoute": "", "menuItems": [{"menuTitle": "Profile", "menuRoute": "profiles/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "User Admin", "menuRoute": "user-admin/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Agencies", "menuRoute": "app/agency/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Users Upload", "menuRoute": "app/user-upload/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Templates", "menuRoute": "app/template-admin/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}], "expand": true, "elementRef": null}, {"menuTitle": "CSR Options", "menuRoute": "app/csr/edit/0", "menuItems": [], "expand": false, "elementRef": null, "roles": []}, {"menuTitle": "CSR Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Electric Operations", "menuRoute": "app/eo/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Electric Operations Search", "menuRoute": "app/eo/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Revenue Operations", "menuRoute": "app/rev/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Revenue Operations Search", "menuRoute": "app/rev/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "<PERSON><PERSON>", "menuRoute": "app/frt/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "<PERSON><PERSON>", "menuRoute": "app/frt/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "Customer Billing", "menuRoute": "app/cbmm/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Customer Billing Search", "menuRoute": "app/cbmm/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "Direct Payment Office", "menuRoute": "app/dpo/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Direct Payment Office Search", "menuRoute": "app/dpo/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "Demand Response", "menuRoute": "app/dr/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Demand Response Search", "menuRoute": "app/dr/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "Customer Care", "menuRoute": "app/cc/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Customer Care Search", "menuRoute": "app/cc/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "Business Center", "menuRoute": "app/csr/edit/0", "menuItems": [], "expand": false, "elementRef": null, "roles": []}, {"menuTitle": "Business Center Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "CSV Extract", "menuRoute": "app/reports/csv-extract", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Summary report", "menuRoute": "app/reports/shutoff-hold-report", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["SysAdmin"], "menuBehavior": "Open", "defaultRoute": "", "createdAt": "2025-05-08T08:43:02.894Z", "updatedAt": "2025-05-08T08:43:02.894Z", "__v": 0}, {"_id": "62e2ae593c8677ac3693cab1", "profileName": "<PERSON><PERSON>", "menuItems": [{"menuTitle": "<PERSON><PERSON>", "menuRoute": "app/frt/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/frt/list", "menuItems": [], "expand": true, "roles": []}], "isActive": true, "isDefault": false, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "createdAt": "2025-05-08T08:43:02.894Z", "updatedAt": "2025-05-08T08:43:02.894Z", "__v": 0}, {"_id": "6331e8754afc3a75729866b5", "profileName": "Direct Payment Office", "menuItems": [{"menuTitle": "Direct Payment Office", "menuRoute": "app/dpo/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/dpo/list", "menuItems": [], "expand": true, "roles": []}], "isActive": true, "isDefault": false, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "createdAt": "2025-05-08T08:43:02.894Z", "updatedAt": "2025-05-08T08:43:02.894Z", "__v": 0}, {"_id": "63619b162537f0b2f82fec49", "profileName": "Demand Response", "menuItems": [{"menuTitle": "Demand Response", "menuRoute": "app/dr/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/dr/list", "menuItems": [], "expand": true, "roles": []}], "isActive": true, "isDefault": false, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "__v": 0, "createdAt": "2025-05-08T08:43:02.894Z", "updatedAt": "2025-05-08T08:43:02.894Z"}, {"_id": "681c6ede380bc470b41bd607", "menuItems": [{"menuTitle": "Past Due Data Extract", "menuRoute": "app/past-due/extract", "roles": ["PAST_DUE_EXTRACT_CSV"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "roles": ["SysAdmin", "PAST_DUE_EXTRACT_CSV"], "profileName": "Past Due Admin", "isDefault": false, "defaultRoute": ""}, {"_id": "681c6ede380bc470b41bd608", "menuItems": [{"menuTitle": "Admin", "menuRoute": "", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "User Admin", "menuRoute": "user-admin/list", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "ASP Search", "menuRoute": "app/asp-customer/list", "roles": ["SEARCH_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "ASP Data Extract", "menuRoute": "app/asp-customer/extract", "roles": ["EXTRACT_CSV"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Rollup Report", "menuRoute": "app/asp-customer/rollup-rpt", "roles": ["ROLLUP_REPORT"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "roles": ["SysAdmin", "EDIT_TEMPLATE", "SEARCH_ASP_CUSTOMER", "SEND_MESSAGES_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "EXTRACT_CSV", "ADD_ASP_CUSTOMER"], "profileName": "ASP User Admin", "isDefault": false, "defaultRoute": ""}, {"_id": "681c6ede380bc470b41bd609", "menuItems": [{"menuTitle": "Admin", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "User Admin", "menuRoute": "user-admin/list", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Templates", "menuRoute": "app/template-admin/list", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "Search", "menuRoute": "app/asp-customer/list", "roles": ["SEARCH_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "ASP Data Extract", "menuRoute": "app/asp-customer/extract", "roles": ["EXTRACT_CSV"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "roles": ["SysAdmin", "EDIT_TEMPLATE", "SEARCH_ASP_CUSTOMER", "SEND_MESSAGES_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "EXTRACT_CSV"], "profileName": "ASP_ADMIN", "isDefault": false, "defaultRoute": ""}, {"_id": "681c6ede380bc470b41bd60a", "menuItems": [{"menuTitle": "Search", "menuRoute": "app/asp-customer/list", "roles": ["SEARCH_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "roles": ["SEARCH_ASP_CUSTOMER", "SEND_MESSAGES_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "ADD_ASP_CUSTOMER"], "profileName": "ASP_CSR", "isDefault": false, "defaultRoute": ""}, {"_id": "681c6ede380bc470b41bd60b", "menuItems": [{"menuTitle": "Admin", "menuRoute": "", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "User Admin", "menuRoute": "user-admin/list", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Templates", "menuRoute": "app/template-admin/list", "roles": ["EDIT_TEMPLATE"], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "ASP Search", "menuRoute": "app/asp-customer/list", "roles": ["SEARCH_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "ASP Data Extract", "menuRoute": "app/asp-customer/extract", "roles": ["EXTRACT_CSV"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Rollup Report", "menuRoute": "app/asp-customer/rollup-rpt", "roles": ["ROLLUP_REPORT"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "roles": ["SysAdmin", "EDIT_TEMPLATE", "SEARCH_ASP_CUSTOMER", "SEND_MESSAGES_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "EXTRACT_CSV", "ADD_ASP_CUSTOMER"], "profileName": "ASP Admin", "isDefault": false, "defaultRoute": ""}, {"_id": "681c6ede380bc470b41bd60c", "menuItems": [{"menuTitle": "CSR Options", "menuRoute": "app/csr/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "profileName": "CSR", "isDefault": true, "defaultRoute": "", "roles": []}, {"_id": "681c6ede380bc470b41bd60d", "menuItems": [{"menuTitle": "Electric Operations", "menuRoute": "app/eo/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/eo/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "profileName": "Electric Operations", "isDefault": true, "defaultRoute": "", "roles": []}, {"_id": "681c6ede380bc470b41bd60e", "menuItems": [{"menuTitle": "Revenue Operations", "menuRoute": "app/rev/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/rev/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "profileName": "Revenue Operations", "isDefault": true, "defaultRoute": "", "roles": []}, {"_id": "681c6ede380bc470b41bd60f", "menuItems": [{"menuTitle": "Customer Billing", "menuRoute": "app/cbmm/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/cbmm/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "CSR Options", "menuRoute": "app/csr/edit/0", "menuItems": [], "expand": false, "elementRef": null, "roles": []}, {"menuTitle": "CSR Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "profileName": "Customer Billing", "isDefault": true, "defaultRoute": "", "roles": []}, {"_id": "681c6ede380bc470b41bd610", "menuItems": [{"menuTitle": "Customer Care", "menuRoute": "app/cc/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Customer Care Search", "menuRoute": "app/cc/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "CSR Options", "menuRoute": "app/csr/edit/0", "menuItems": [], "expand": false, "elementRef": null, "roles": []}, {"menuTitle": "CSR Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "profileName": "Customer Care", "isDefault": true, "defaultRoute": "", "roles": []}, {"_id": "681c6ede380bc470b41bd611", "menuItems": [{"menuTitle": "Business Center", "menuRoute": "app/csr/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Business Center Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "profileName": "Business Center", "isDefault": true, "defaultRoute": "", "roles": []}, {"_id": "681c6ede380bc470b41bd612", "menuItems": [{"menuTitle": "Admin", "menuRoute": "", "menuItems": [{"menuTitle": "Profile", "menuRoute": "profiles/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "User Admin", "menuRoute": "user-admin/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Agencies", "menuRoute": "app/agency/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Users Upload", "menuRoute": "app/user-upload/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Templates", "menuRoute": "app/template-admin/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}], "expand": true, "elementRef": null}, {"menuTitle": "CSR Options", "menuRoute": "app/csr/edit/0", "menuItems": [], "expand": false, "elementRef": null, "roles": []}, {"menuTitle": "CSR Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Electric Operations", "menuRoute": "app/eo/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Electric Operations Search", "menuRoute": "app/eo/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Revenue Operations", "menuRoute": "app/rev/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Revenue Operations Search", "menuRoute": "app/rev/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "<PERSON><PERSON>", "menuRoute": "app/frt/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "<PERSON><PERSON>", "menuRoute": "app/frt/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "Customer Billing", "menuRoute": "app/cbmm/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Customer Billing Search", "menuRoute": "app/cbmm/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "Direct Payment Office", "menuRoute": "app/dpo/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Direct Payment Office Search", "menuRoute": "app/dpo/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "Demand Response", "menuRoute": "app/dr/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Demand Response Search", "menuRoute": "app/dr/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "Customer Care", "menuRoute": "app/cc/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Customer Care Search", "menuRoute": "app/cc/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "Business Center", "menuRoute": "app/csr/edit/0", "menuItems": [], "expand": false, "elementRef": null, "roles": []}, {"menuTitle": "Business Center Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "CSV Extract", "menuRoute": "app/reports/csv-extract", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Summary report", "menuRoute": "app/reports/shutoff-hold-report", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "profileName": "Super Admin", "isDefault": false, "roles": ["SysAdmin"], "defaultRoute": ""}, {"_id": "681c6ede380bc470b41bd613", "menuItems": [{"menuTitle": "<PERSON><PERSON>", "menuRoute": "app/frt/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/frt/list", "menuItems": [], "expand": true, "roles": []}], "isActive": true, "profileName": "<PERSON><PERSON>", "isDefault": false, "roles": [], "defaultRoute": ""}, {"_id": "681c6ede380bc470b41bd614", "menuItems": [{"menuTitle": "Direct Payment Office", "menuRoute": "app/dpo/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/dpo/list", "menuItems": [], "expand": true, "roles": []}], "isActive": true, "profileName": "Direct Payment Office", "isDefault": false, "roles": [], "defaultRoute": ""}, {"_id": "681c6ede380bc470b41bd615", "menuItems": [{"menuTitle": "Demand Response", "menuRoute": "app/dr/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/dr/list", "menuItems": [], "expand": true, "roles": []}], "isActive": true, "profileName": "Demand Response", "isDefault": false, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "__v": 0}, {"_id": "681c6ff13d57f610c413920c", "menuItems": [{"menuTitle": "Past Due Data Extract", "menuRoute": "app/past-due/extract", "roles": ["PAST_DUE_EXTRACT_CSV"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "roles": ["SysAdmin", "PAST_DUE_EXTRACT_CSV"], "profileName": "Past Due Admin", "isDefault": false, "defaultRoute": ""}, {"_id": "681c6ff13d57f610c413920d", "menuItems": [{"menuTitle": "Admin", "menuRoute": "", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "User Admin", "menuRoute": "user-admin/list", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "ASP Search", "menuRoute": "app/asp-customer/list", "roles": ["SEARCH_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "ASP Data Extract", "menuRoute": "app/asp-customer/extract", "roles": ["EXTRACT_CSV"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Rollup Report", "menuRoute": "app/asp-customer/rollup-rpt", "roles": ["ROLLUP_REPORT"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "roles": ["SysAdmin", "EDIT_TEMPLATE", "SEARCH_ASP_CUSTOMER", "SEND_MESSAGES_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "EXTRACT_CSV", "ADD_ASP_CUSTOMER"], "profileName": "ASP User Admin", "isDefault": false, "defaultRoute": ""}, {"_id": "681c6ff13d57f610c413920e", "menuItems": [{"menuTitle": "Admin", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "User Admin", "menuRoute": "user-admin/list", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Templates", "menuRoute": "app/template-admin/list", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "Search", "menuRoute": "app/asp-customer/list", "roles": ["SEARCH_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "ASP Data Extract", "menuRoute": "app/asp-customer/extract", "roles": ["EXTRACT_CSV"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "roles": ["SysAdmin", "EDIT_TEMPLATE", "SEARCH_ASP_CUSTOMER", "SEND_MESSAGES_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "EXTRACT_CSV"], "profileName": "ASP_ADMIN", "isDefault": false, "defaultRoute": ""}, {"_id": "681c6ff13d57f610c413920f", "menuItems": [{"menuTitle": "Search", "menuRoute": "app/asp-customer/list", "roles": ["SEARCH_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "roles": ["SEARCH_ASP_CUSTOMER", "SEND_MESSAGES_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "ADD_ASP_CUSTOMER"], "profileName": "ASP_CSR", "isDefault": false, "defaultRoute": ""}, {"_id": "681c6ff13d57f610c4139210", "menuItems": [{"menuTitle": "Admin", "menuRoute": "", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "User Admin", "menuRoute": "user-admin/list", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Templates", "menuRoute": "app/template-admin/list", "roles": ["EDIT_TEMPLATE"], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "ASP Search", "menuRoute": "app/asp-customer/list", "roles": ["SEARCH_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "ASP Data Extract", "menuRoute": "app/asp-customer/extract", "roles": ["EXTRACT_CSV"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Rollup Report", "menuRoute": "app/asp-customer/rollup-rpt", "roles": ["ROLLUP_REPORT"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "roles": ["SysAdmin", "EDIT_TEMPLATE", "SEARCH_ASP_CUSTOMER", "SEND_MESSAGES_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "EXTRACT_CSV", "ADD_ASP_CUSTOMER"], "profileName": "ASP Admin", "isDefault": false, "defaultRoute": ""}, {"_id": "681c6ff13d57f610c4139211", "menuItems": [{"menuTitle": "CSR Options", "menuRoute": "app/csr/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "profileName": "CSR", "isDefault": true, "defaultRoute": "", "roles": []}, {"_id": "681c6ff13d57f610c4139212", "menuItems": [{"menuTitle": "Electric Operations", "menuRoute": "app/eo/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/eo/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "profileName": "Electric Operations", "isDefault": true, "defaultRoute": "", "roles": []}, {"_id": "681c6ff13d57f610c4139213", "menuItems": [{"menuTitle": "Revenue Operations", "menuRoute": "app/rev/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/rev/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "profileName": "Revenue Operations", "isDefault": true, "defaultRoute": "", "roles": []}, {"_id": "681c6ff13d57f610c4139214", "menuItems": [{"menuTitle": "Customer Billing", "menuRoute": "app/cbmm/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/cbmm/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "CSR Options", "menuRoute": "app/csr/edit/0", "menuItems": [], "expand": false, "elementRef": null, "roles": []}, {"menuTitle": "CSR Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "profileName": "Customer Billing", "isDefault": true, "defaultRoute": "", "roles": []}, {"_id": "681c6ff13d57f610c4139215", "menuItems": [{"menuTitle": "Customer Care", "menuRoute": "app/cc/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Customer Care Search", "menuRoute": "app/cc/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "CSR Options", "menuRoute": "app/csr/edit/0", "menuItems": [], "expand": false, "elementRef": null, "roles": []}, {"menuTitle": "CSR Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "profileName": "Customer Care", "isDefault": true, "defaultRoute": "", "roles": []}, {"_id": "681c6ff13d57f610c4139216", "menuItems": [{"menuTitle": "Business Center", "menuRoute": "app/csr/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Business Center Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "profileName": "Business Center", "isDefault": true, "defaultRoute": "", "roles": []}, {"_id": "681c6ff13d57f610c4139217", "menuItems": [{"menuTitle": "Admin", "menuRoute": "", "menuItems": [{"menuTitle": "Profile", "menuRoute": "profiles/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "User Admin", "menuRoute": "user-admin/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Agencies", "menuRoute": "app/agency/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Users Upload", "menuRoute": "app/user-upload/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Templates", "menuRoute": "app/template-admin/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}], "expand": true, "elementRef": null}, {"menuTitle": "CSR Options", "menuRoute": "app/csr/edit/0", "menuItems": [], "expand": false, "elementRef": null, "roles": []}, {"menuTitle": "CSR Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Electric Operations", "menuRoute": "app/eo/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Electric Operations Search", "menuRoute": "app/eo/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Revenue Operations", "menuRoute": "app/rev/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Revenue Operations Search", "menuRoute": "app/rev/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "<PERSON><PERSON>", "menuRoute": "app/frt/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "<PERSON><PERSON>", "menuRoute": "app/frt/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "Customer Billing", "menuRoute": "app/cbmm/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Customer Billing Search", "menuRoute": "app/cbmm/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "Direct Payment Office", "menuRoute": "app/dpo/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Direct Payment Office Search", "menuRoute": "app/dpo/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "Demand Response", "menuRoute": "app/dr/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Demand Response Search", "menuRoute": "app/dr/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "Customer Care", "menuRoute": "app/cc/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Customer Care Search", "menuRoute": "app/cc/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "Business Center", "menuRoute": "app/csr/edit/0", "menuItems": [], "expand": false, "elementRef": null, "roles": []}, {"menuTitle": "Business Center Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "CSV Extract", "menuRoute": "app/reports/csv-extract", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Summary report", "menuRoute": "app/reports/shutoff-hold-report", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "profileName": "Super Admin", "isDefault": false, "roles": ["SysAdmin"], "defaultRoute": ""}, {"_id": "681c6ff13d57f610c4139218", "menuItems": [{"menuTitle": "<PERSON><PERSON>", "menuRoute": "app/frt/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/frt/list", "menuItems": [], "expand": true, "roles": []}], "isActive": true, "profileName": "<PERSON><PERSON>", "isDefault": false, "roles": [], "defaultRoute": ""}, {"_id": "681c6ff13d57f610c4139219", "menuItems": [{"menuTitle": "Direct Payment Office", "menuRoute": "app/dpo/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/dpo/list", "menuItems": [], "expand": true, "roles": []}], "isActive": true, "profileName": "Direct Payment Office", "isDefault": false, "roles": [], "defaultRoute": ""}, {"_id": "681c6ff13d57f610c413921a", "menuItems": [{"menuTitle": "Demand Response", "menuRoute": "app/dr/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/dr/list", "menuItems": [], "expand": true, "roles": []}], "isActive": true, "profileName": "Demand Response", "isDefault": false, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "__v": 0}]