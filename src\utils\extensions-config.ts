import fs from 'fs';
import path from 'path';

export interface ProjectConfig {
  name: string;
  path: string;
}

export interface ExtensionsConfig {
  projects: ProjectConfig[];
}

/**
 * Utility class for managing extensions configuration
 * Can be used from Node.js environment (tasks, scripts, etc.)
 */
export class ExtensionsConfigManager {
  private configPath: string;
  private projectsDir: string;

  constructor(configPath?: string, projectsDir?: string) {
    this.configPath = configPath || path.join(__dirname, '../config/extensions.json');
    this.projectsDir = projectsDir || path.join(__dirname, '../../projects');
  }

  /**
   * Load current configuration
   */
  loadConfig(): ExtensionsConfig {
    try {
      if (fs.existsSync(this.configPath)) {
        const content = fs.readFileSync(this.configPath, 'utf8');
        return JSON.parse(content);
      }
    } catch (error) {
      console.warn('Error loading extensions config:', error);
    }
    
    return { projects: [] };
  }

  /**
   * Save configuration
   */
  saveConfig(config: ExtensionsConfig): void {
    const dir = path.dirname(this.configPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    fs.writeFileSync(this.configPath, JSON.stringify(config, null, 2));
  }

  /**
   * Scan the projects directory and automatically detect extensions
   */
  scanProjectsDirectory(): ProjectConfig[] {
    const projects: ProjectConfig[] = [];
    
    if (!fs.existsSync(this.projectsDir)) {
      console.warn('Projects directory not found:', this.projectsDir);
      return projects;
    }

    // Recursively scan for extension/sdt.ts files
    const scanDirectory = (dir: string, relativePath = '') => {
      const items = fs.readdirSync(dir, { withFileTypes: true });
      
      for (const item of items) {
        if (item.isDirectory()) {
          const itemPath = path.join(dir, item.name);
          const newRelativePath = relativePath ? `${relativePath}/${item.name}` : item.name;
          
          // Check if this directory has an extension/sdt.ts file
          const extensionPath = path.join(itemPath, 'extension', 'sdt.ts');
          if (fs.existsSync(extensionPath)) {
            projects.push({
              name: item.name,
              path: newRelativePath
            });
          } else {
            // Continue scanning subdirectories
            scanDirectory(itemPath, newRelativePath);
          }
        }
      }
    };

    scanDirectory(this.projectsDir);
    return projects;
  }

  /**
   * Add or update a project in the configuration
   */
  addProject(name: string, projectPath: string): void {
    const config = this.loadConfig();
    
    // Check if project already exists
    const existingIndex = config.projects.findIndex(p => p.name === name);
    
    if (existingIndex >= 0) {
      config.projects[existingIndex] = { name, path: projectPath };
    } else {
      config.projects.push({ name, path: projectPath });
    }
    
    this.saveConfig(config);
  }

  /**
   * Remove a project from the configuration
   */
  removeProject(name: string): boolean {
    const config = this.loadConfig();
    const initialLength = config.projects.length;
    
    config.projects = config.projects.filter(p => p.name !== name);
    
    if (config.projects.length < initialLength) {
      this.saveConfig(config);
      return true;
    }
    
    return false;
  }

  /**
   * Auto-scan and update configuration
   */
  autoScan(): ProjectConfig[] {
    const scannedProjects = this.scanProjectsDirectory();
    const config: ExtensionsConfig = { projects: scannedProjects };
    
    this.saveConfig(config);
    return scannedProjects;
  }

  /**
   * Get all projects
   */
  getProjects(): ProjectConfig[] {
    return this.loadConfig().projects;
  }
}

// Export a default instance
export const extensionsConfigManager = new ExtensionsConfigManager();
