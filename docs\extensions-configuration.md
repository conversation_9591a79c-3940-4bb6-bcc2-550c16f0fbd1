# Extensions Configuration Management

The SDT project now supports dynamic extension configuration that can be updated from the Node.js environment. The system uses a hybrid approach that combines static imports (for webpack compatibility) with dynamic configuration (for runtime flexibility).

## Configuration File

The extension configuration is stored in `src/config/extensions.json`:

```json
{
  "projects": [
    { "name": "ce", "path": "riverstar/ce" },
    { "name": "billing", "path": "riverstar/billing" },
    { "name": "jba", "path": "dorian solutions/jba" },
    { "name": "pwv", "path": "riverstar/pwv" },
    { "name": "riverstar", "path": "riverstar/riverstar" },
    { "name": "rwa", "path": "other/rwa" },
    { "name": "srm", "path": "riverstar/srm" }
  ]
}
```

## Static Imports + Dynamic Configuration

The system uses a hybrid approach:

1. **Static Imports**: All discovered extensions are added as static imports in `src/app/sdt.ts` for webpack compatibility
2. **Dynamic Configuration**: The Cypress configuration filters which extensions are active at runtime

```typescript
// Static imports in sdt.ts (auto-generated)
const staticImportMap: Record<string, () => Promise<any>> = {
  ce: () => import("../../projects/riverstar/ce/extension/sdt"),
  billing: () => import("../../projects/riverstar/billing/extension/sdt"),
  // ... other extensions
};

// Runtime filtering based on Cypress environment
const extensionImportMap = Cypress.env("extensionImportMap") || {};
```

## Managing Extensions from Node.js

### Using npm Scripts

```bash
# Auto-scan the projects directory and update configuration
npm run extensions:scan

# List current extensions
npm run extensions:list

# Add a new extension
npm run extensions:add myproject "organization/myproject"

# Remove an extension
npm run extensions:remove myproject

# Sync static imports with discovered extensions
npm run extensions:sync
```

### Using the Command Line Script Directly

```bash
# Auto-scan projects directory
node scripts/update-extensions-config.js scan

# List current projects
node scripts/update-extensions-config.js list

# Add/update a project
node scripts/update-extensions-config.js add <name> <path>

# Remove a project
node scripts/update-extensions-config.js remove <name>
```

### Using the TypeScript API

```typescript
import { ExtensionsConfigManager } from '@/utils/extensions-config';

const configManager = new ExtensionsConfigManager();

// Auto-scan and update configuration
const projects = configManager.autoScan();

// Add a project
configManager.addProject('myproject', 'organization/myproject');

// Remove a project
configManager.removeProject('myproject');

// Get all projects
const allProjects = configManager.getProjects();
```

## Managing Extensions from Cypress Tests

You can also manage extensions from within Cypress tests using the provided actions:

### Using Cypress Tasks

```javascript
// Auto-scan extensions
cy.task('extensionsConfigScan');

// Add an extension
cy.task('extensionsConfigAdd', { name: 'myproject', path: 'organization/myproject' });

// Remove an extension
cy.task('extensionsConfigRemove', 'myproject');

// List all extensions
cy.task('extensionsConfigList');
```

### Using SDT Actions

You can use these actions in your test steps:

- **"Scan Extensions"** - Auto-scan the projects directory
- **"Add Extension"** - Add a new extension (requires name and path in simpleValues)
- **"Remove Extension"** - Remove an extension (requires name in simpleValues[0])
- **"List Extensions"** - List all current extensions
- **"Sync Extension Imports"** - Sync static imports with discovered extensions

**Note**: After making changes to extensions, restart Cypress to pick up the changes.

## How It Works

1. **Extension Discovery**: Scripts scan the `projects` directory for folders with `extension/sdt.ts` files.

2. **Static Import Sync**: The `sync-static-imports.js` script updates the static import map in `sdt.ts` with all discovered extensions.

3. **Cypress Configuration**: `cypress.config.ts` filters extensions based on `extensions.json` and adds them to the Cypress environment.

4. **Runtime Filtering**: `Sdt.getExtensionMap()` uses only the configured extensions from the static import map.

5. **Webpack Compatibility**: Static imports ensure webpack can bundle all extensions, while configuration provides runtime flexibility.

6. **Multiple Interfaces**: You can manage extensions through npm scripts, direct Node.js scripts, TypeScript API, or Cypress tasks.

## Project Structure Requirements

For a project to be automatically detected, it must have the following structure:

```
projects/
├── organization/
│   └── myproject/
│       └── extension/
│           └── sdt.ts    # Required extension file
```

The auto-scan feature will recursively search the projects directory and register any folder that contains an `extension/sdt.ts` file.

## Benefits

- **No Code Changes**: Add or remove extensions without modifying source code
- **Integrated Configuration**: Extensions are configured directly in Cypress startup
- **Multiple Management Options**: CLI, API, and Cypress integration
- **Auto-Discovery**: Automatically detect new extensions
- **Maintainable**: Centralized configuration management
- **Bundler Compatible**: Uses dynamic imports that work with all bundlers
- **Simple Restart**: Changes take effect with a simple Cypress restart
