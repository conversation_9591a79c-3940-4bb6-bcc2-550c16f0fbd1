# Extensions Configuration Management

The SDT project now supports dynamic extension configuration that can be updated from the Node.js environment. The system automatically scans for extensions and embeds the configuration directly in the Cypress configuration.

## Configuration File

The extension configuration is stored in `src/config/extensions.json`:

```json
{
  "projects": [
    { "name": "ce", "path": "riverstar/ce" },
    { "name": "billing", "path": "riverstar/billing" },
    { "name": "jba", "path": "dorian solutions/jba" },
    { "name": "pwv", "path": "riverstar/pwv" },
    { "name": "riverstar", "path": "riverstar/riverstar" },
    { "name": "rwa", "path": "other/rwa" },
    { "name": "srm", "path": "riverstar/srm" }
  ]
}
```

## Cypress Configuration Integration

The system automatically scans for extensions during Cypress startup and embeds the configuration directly in the Cypress environment. This happens in `cypress.config.ts`:

```typescript
// Extensions are automatically discovered and added to Cypress env
config.env.extensionImportMap = {
  "ce": "./projects/riverstar/ce/extension/sdt",
  "billing": "./projects/riverstar/billing/extension/sdt",
  // ... other extensions
};
```

## Managing Extensions from Node.js

### Using npm Scripts

```bash
# Auto-scan the projects directory and update configuration
npm run extensions:scan

# List current extensions
npm run extensions:list

# Add a new extension
npm run extensions:add myproject "organization/myproject"

# Remove an extension
npm run extensions:remove myproject
```

### Using the Command Line Script Directly

```bash
# Auto-scan projects directory
node scripts/update-extensions-config.js scan

# List current projects
node scripts/update-extensions-config.js list

# Add/update a project
node scripts/update-extensions-config.js add <name> <path>

# Remove a project
node scripts/update-extensions-config.js remove <name>
```

### Using the TypeScript API

```typescript
import { ExtensionsConfigManager } from '@/utils/extensions-config';

const configManager = new ExtensionsConfigManager();

// Auto-scan and update configuration
const projects = configManager.autoScan();

// Add a project
configManager.addProject('myproject', 'organization/myproject');

// Remove a project
configManager.removeProject('myproject');

// Get all projects
const allProjects = configManager.getProjects();
```

## Managing Extensions from Cypress Tests

You can also manage extensions from within Cypress tests using the provided actions:

### Using Cypress Tasks

```javascript
// Auto-scan extensions
cy.task('extensionsConfigScan');

// Add an extension
cy.task('extensionsConfigAdd', { name: 'myproject', path: 'organization/myproject' });

// Remove an extension
cy.task('extensionsConfigRemove', 'myproject');

// List all extensions
cy.task('extensionsConfigList');
```

### Using SDT Actions

You can use these actions in your test steps:

- **"Scan Extensions"** - Auto-scan the projects directory
- **"Add Extension"** - Add a new extension (requires name and path in simpleValues)
- **"Remove Extension"** - Remove an extension (requires name in simpleValues[0])
- **"List Extensions"** - List all current extensions

**Note**: After making changes to extensions, restart Cypress to pick up the changes.

## How It Works

1. **Cypress Startup Scanning**: When Cypress starts, `cypress.config.ts` automatically scans the `projects` directory for extensions.

2. **Configuration Integration**: The discovered extensions are filtered based on `extensions.json` and embedded in the Cypress environment.

3. **Dynamic Import Resolution**: At runtime, `Sdt.getExtensionMap()` uses the Cypress environment configuration to create dynamic imports.

4. **Configuration Management**: Extensions can be added/removed via the configuration file, with changes taking effect on the next Cypress restart.

5. **Multiple Interfaces**: You can manage extensions through npm scripts, direct Node.js scripts, TypeScript API, or Cypress tasks.

## Project Structure Requirements

For a project to be automatically detected, it must have the following structure:

```
projects/
├── organization/
│   └── myproject/
│       └── extension/
│           └── sdt.ts    # Required extension file
```

The auto-scan feature will recursively search the projects directory and register any folder that contains an `extension/sdt.ts` file.

## Benefits

- **No Code Changes**: Add or remove extensions without modifying source code
- **Integrated Configuration**: Extensions are configured directly in Cypress startup
- **Multiple Management Options**: CLI, API, and Cypress integration
- **Auto-Discovery**: Automatically detect new extensions
- **Maintainable**: Centralized configuration management
- **Bundler Compatible**: Uses dynamic imports that work with all bundlers
- **Simple Restart**: Changes take effect with a simple Cypress restart
