# Extensions Configuration Management

The SDT project now supports dynamic extension configuration that can be updated from the Node.js environment. This system uses a two-phase approach:

1. **Configuration Phase**: Manage which extensions are active via `src/config/extensions.json`
2. **Build Phase**: Generate static imports at build time to ensure compatibility with bundlers

## Configuration File

The extension configuration is stored in `src/config/extensions.json`:

```json
{
  "projects": [
    { "name": "ce", "path": "riverstar/ce" },
    { "name": "billing", "path": "riverstar/billing" },
    { "name": "jba", "path": "dorian solutions/jba" },
    { "name": "pwv", "path": "riverstar/pwv" },
    { "name": "riverstar", "path": "riverstar/riverstar" },
    { "name": "rwa", "path": "other/rwa" },
    { "name": "srm", "path": "riverstar/srm" }
  ]
}
```

## Generated Imports File

The system automatically generates `src/app/generated-extension-imports.ts` which contains static imports for all discovered extensions. This file is auto-generated and should not be edited manually.

```typescript
// Example generated file
export const extensionImportMap: Record<string, () => Promise<any>> = {
  "ce": () => import("../../projects/riverstar/ce/extension/sdt"),
  "billing": () => import("../../projects/riverstar/billing/extension/sdt"),
  // ... other extensions
};
```

## Managing Extensions from Node.js

### Using npm Scripts

```bash
# Auto-scan the projects directory and update configuration
npm run extensions:scan

# List current extensions
npm run extensions:list

# Add a new extension
npm run extensions:add myproject "organization/myproject"

# Remove an extension
npm run extensions:remove myproject

# Generate static imports (usually done automatically)
npm run extensions:generate
```

### Using the Command Line Script Directly

```bash
# Auto-scan projects directory
node scripts/update-extensions-config.js scan

# List current projects
node scripts/update-extensions-config.js list

# Add/update a project
node scripts/update-extensions-config.js add <name> <path>

# Remove a project
node scripts/update-extensions-config.js remove <name>
```

### Using the TypeScript API

```typescript
import { ExtensionsConfigManager } from '@/utils/extensions-config';

const configManager = new ExtensionsConfigManager();

// Auto-scan and update configuration
const projects = configManager.autoScan();

// Add a project
configManager.addProject('myproject', 'organization/myproject');

// Remove a project
configManager.removeProject('myproject');

// Get all projects
const allProjects = configManager.getProjects();
```

## Managing Extensions from Cypress Tests

You can also manage extensions from within Cypress tests using the provided actions:

### Using Cypress Tasks

```javascript
// Auto-scan extensions
cy.task('extensionsConfigScan');

// Add an extension
cy.task('extensionsConfigAdd', { name: 'myproject', path: 'organization/myproject' });

// Remove an extension
cy.task('extensionsConfigRemove', 'myproject');

// List all extensions
cy.task('extensionsConfigList');
```

### Using SDT Actions

You can use these actions in your test steps:

- **"Scan Extensions"** - Auto-scan the projects directory
- **"Add Extension"** - Add a new extension (requires name and path in simpleValues)
- **"Remove Extension"** - Remove an extension (requires name in simpleValues[0])
- **"List Extensions"** - List all current extensions
- **"Generate Extension Imports"** - Regenerate the static imports file

## How It Works

1. **Two-Phase System**:
   - **Configuration Phase**: Manage active extensions via `extensions.json`
   - **Build Phase**: Generate static imports that bundlers can analyze

2. **Auto-Discovery**: The system scans the `projects` directory to find folders with `extension/sdt.ts` files.

3. **Static Import Generation**: The `generate-extension-imports.js` script creates a TypeScript file with static imports for all discovered extensions.

4. **Dynamic Filtering**: At runtime, `Sdt.getExtensionMap()` filters the static imports based on the configuration file.

5. **Automatic Regeneration**: When extensions are added/removed via tasks, the static imports are automatically regenerated.

6. **Multiple Interfaces**: You can manage extensions through npm scripts, direct Node.js scripts, TypeScript API, or Cypress tasks.

## Project Structure Requirements

For a project to be automatically detected, it must have the following structure:

```
projects/
├── organization/
│   └── myproject/
│       └── extension/
│           └── sdt.ts    # Required extension file
```

The auto-scan feature will recursively search the projects directory and register any folder that contains an `extension/sdt.ts` file.

## Benefits

- **No Code Changes**: Add or remove extensions without modifying source code
- **Dynamic Configuration**: Changes are picked up at runtime
- **Multiple Management Options**: CLI, API, and Cypress integration
- **Auto-Discovery**: Automatically detect new extensions
- **Maintainable**: Centralized configuration management
