# Extensions Configuration Management

The SDT project now supports dynamic extension configuration that can be updated from the Node.js environment. The system uses a simple approach where Cypress configuration scans for extensions and provides import paths that are used directly in dynamic imports.

## Configuration File

The extension configuration is stored in `src/config/extensions.json`:

```json
{
  "projects": [
    { "name": "ce", "path": "riverstar/ce" },
    { "name": "billing", "path": "riverstar/billing" },
    { "name": "jba", "path": "dorian solutions/jba" },
    { "name": "pwv", "path": "riverstar/pwv" },
    { "name": "riverstar", "path": "riverstar/riverstar" },
    { "name": "rwa", "path": "other/rwa" },
    { "name": "srm", "path": "riverstar/srm" }
  ]
}
```

## Simple Dynamic Import Approach

The system uses a straightforward approach:

1. **Extension Discovery**: Cypress configuration scans the projects directory for extensions
2. **Import Path Mapping**: Creates a map of extension names to their import paths
3. **Direct Dynamic Import**: The SDT class uses the import path directly in a dynamic import

```typescript
// In loadExtension method
const projectName = Cypress.env().app;
const allExtensionsMap = Cypress.env("allExtensionsMap") || {};
const importPath = allExtensionsMap[projectName];

// Direct dynamic import using the path from configuration
const extension = await import(importPath);
```

## Managing Extensions

Extensions are automatically discovered by scanning the projects directory. To add or remove extensions, simply:

1. **Add Extension**: Create a new folder in the projects directory with the structure `extension/sdt.ts`
2. **Remove Extension**: Delete the extension folder from the projects directory
3. **Configure Active Extensions**: Edit `src/config/extensions.json` to control which discovered extensions are active

The system will automatically pick up changes on the next Cypress restart.

## How It Works

1. **Extension Discovery**: When Cypress starts, `cypress.config.ts` scans the `projects` directory for folders with `extension/sdt.ts` files.

2. **Configuration Filtering**: Discovered extensions are filtered based on `src/config/extensions.json` to determine which are active.

3. **Environment Setup**: Active extensions and their import paths are added to the Cypress environment variables.

4. **Dynamic Loading**: The `Sdt.loadExtension()` method gets the import path from the environment and uses it in a dynamic import.

5. **Simple and Direct**: No static imports or build scripts needed - just direct dynamic imports using paths from configuration.

## Project Structure Requirements

For a project to be automatically detected, it must have the following structure:

```
projects/
├── organization/
│   └── myproject/
│       └── extension/
│           └── sdt.ts    # Required extension file
```

The auto-scan feature will recursively search the projects directory and register any folder that contains an `extension/sdt.ts` file.

## Benefits

- **Simple Architecture**: No build scripts, static imports, or generated files
- **Direct Dynamic Imports**: Uses import paths directly from configuration
- **Auto-Discovery**: Automatically finds extensions in the projects directory
- **Configuration-Based**: Control active extensions via JSON configuration
- **Webpack Compatible**: Dynamic imports work with all bundlers
- **Easy Management**: Just add/remove folders and restart Cypress
- **Clean Codebase**: Minimal complexity and maintenance overhead
