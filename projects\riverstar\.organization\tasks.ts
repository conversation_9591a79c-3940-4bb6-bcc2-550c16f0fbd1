import { ExtensionsConfigManager } from "@/utils/extensions-config";
import { ObjectId } from "mongodb";
import { exec } from "child_process";
import fs from "fs-extra";
import path from "path";
import { promisify } from "util";
import writeResults from "@/app/plugins/writeResults/writeResults";

const execAsync = promisify(exec);

const dbTasks = {
  dbDropDatabase: async () => {
    return await global.db.dropDatabase();
  },

  dbDropCollection: async (collection) => {
    return await global.db.dropCollection(collection);
  },

  dbReadAllCollections: async () => {
    return await global.db.listCollections().toArray();
  },

  dbCountDocuments: async (collection) => {
    return await global.db.collection(collection).countDocuments();
  },

  dbDeleteCollection: async (collectionName) => {
    return await global.db.collection(collectionName).drop();
  },

  dbDeleteE2eUsers: async (usersToKeep = ["admin"]) => {
    return await global.db
      .collection("authusers")
      .deleteMany({ username: { $nin: usersToKeep } });
  },

  dbDeleteE2eProfiles: async (profilesToKeep = ["Admin", "Base User"]) => {
    return await global.db
      .collection("profiles")
      .deleteMany({ profileName: { $nin: profilesToKeep } });
  },

  dbReadProfile: async (profileName) => {
    return await global.db.collection("profiles").findOne({
      profileName: profileName,
    });
  },

  dbReadAllUsers: async () => {
    return await global.db.collection("authusers").find().toArray();
  },

  dbReadUserWithId: async (userId) => {
    return await global.db.collection("authusers").findOne({
      _id: new ObjectId(userId),
    });
  },

  dbReadUser: async (user) => {
    return await global.db.collection("authusers").findOne(user);
  },

  dbReadUserWithProfileName: async (profileName) => {
    return await global.db
      .collection("profiles")
      .findOne({
        profileName: profileName,
      })
      .then((profile) =>
        global.db.collection("authusers").findOne({ profile: profile._id })
      );
  },

  dbGetLastEmail: async () => {
    return await global.db
      .collection("email")
      .find()
      .sort({ _id: -1 })
      .limit(1)
      .toArray()
      .then((emails) => emails[0].html);
  },

  dbReadUserPassword: async () => {
    return await global.db
      .collection("email")
      .find()
      .sort({ _id: -1 })
      .toArray();
  },

  exportDb: async (folderName) => {
    let collections = await global.db.listCollections().toArray();

    if (fs.existsSync(folderName)) {
      const files = fs.readdirSync(folderName);
      files.forEach((file) => {
        const filePath = path.join(folderName, file);
        fs.unlinkSync(filePath);
      });
    } else {
      fs.mkdirSync(folderName, { recursive: true });
    }

    for (const collection of collections) {
      const collectionName = collection.name;
      if (collectionName === "sessions") continue;
      if (collectionName === "auditTrail") continue;
      const documents = await global.db
        .collection(collectionName)
        .find()
        .toArray();
      const filePath = path.join(folderName, `${collectionName}.json`);
      fs.writeFileSync(filePath, JSON.stringify(documents, null, 2));
    }

    return null;
  },

  setDb: async (folderName) => {
    const setObjectId = (doc) => {
      const processNestedObjects = (obj) => {
        Object.keys(obj).forEach((key) => {
          if (typeof obj[key] === "object" && obj[key] !== null) {
            processNestedObjects(obj[key]);
          }
          if (
            key === "_id" ||
            (key.endsWith("Id") && obj[key]?.length === 24) ||
            key === "profile"
          ) {
            obj[key] = new ObjectId(obj[key]);
          }
        });
      };

      processNestedObjects(doc);
      return doc;
    };

    if (fs.existsSync(folderName)) {
      const files = fs.readdirSync(folderName);

      for (const file of files) {
        const collectionName = file.replace(".json", "");
        if (collectionName === "sessions") continue;
        if (collectionName === "auditTrail") continue;

        const filePath = path.join(folderName, file);
        const fileContents = fs.readFileSync(filePath, "utf8");
        let documents = JSON.parse(fileContents);
        documents = documents.map((doc) => {
          return setObjectId(doc);
        });
        await global.db.collection(collectionName).deleteMany({});
        for (const document of documents) {
          await global.db.collection(collectionName).insertOne(document);
        }
      }
    }

    return null;
  },
};

const fileTasks = {
  createFolder: (folderPath) => {
    if (!fs.existsSync(folderPath)) {
      fs.mkdirSync(folderPath);
    }
    return null;
  },

  deleteFolder: (folderPath) => {
    if (fs.existsSync(folderPath)) {
      fs.rmdirSync(folderPath, {
        recursive: true,
      });
    }
    return null;
  },

  readFile: (filePath) => {
    if (fs.existsSync(filePath)) {
      const data = fs.readFileSync(filePath);
      return JSON.parse(data.toString());
    }
    return null;
  },

  moveFile: ({ sourceFilePath, destinationFilePath }) => {
    if (fs.existsSync(sourceFilePath)) {
      fs.renameSync(sourceFilePath, destinationFilePath);
    }
    return null;
  },

  moveFiles: ({ sourceFolder, destinationFolder, filesToMoveRegexp }) => {
    if (fs.existsSync(sourceFolder)) {
      fs.readdirSync(sourceFolder)
        .filter((file) => {
          if (!filesToMoveRegexp) {
            return true;
          }
          return file.match(filesToMoveRegexp);
        })
        .forEach((file) => {
          fs.copySync(
            `${sourceFolder}/${file}`,
            `${destinationFolder}/${file}`
          );
          fs.unlinkSync(`${sourceFolder}/${file}`);
        });
    }
    return null;
  },
};

const extensionConfigTasks = {
  extensionsConfigScan: async () => {
    const configManager = new ExtensionsConfigManager();
    const projects = configManager.autoScan();
    console.log("Auto-scanned extensions:", projects);

    // Regenerate static imports
    try {
      await execAsync("node scripts/generate-extension-imports.js");
      console.log("Regenerated extension imports");
    } catch (error) {
      console.warn("Failed to regenerate extension imports:", error.message);
    }

    return projects;
  },

  extensionsConfigAdd: async ({ name, path: projectPath }) => {
    const configManager = new ExtensionsConfigManager();
    configManager.addProject(name, projectPath);
    console.log(`Added extension: ${name} -> ${projectPath}`);

    // Regenerate static imports
    try {
      await execAsync("node scripts/generate-extension-imports.js");
      console.log("Regenerated extension imports");
    } catch (error) {
      console.warn("Failed to regenerate extension imports:", error.message);
    }

    return null;
  },

  extensionsConfigRemove: async (name) => {
    const configManager = new ExtensionsConfigManager();
    const removed = configManager.removeProject(name);
    if (removed) {
      console.log(`Removed extension: ${name}`);

      // Regenerate static imports
      try {
        await execAsync("node scripts/generate-extension-imports.js");
        console.log("Regenerated extension imports");
      } catch (error) {
        console.warn("Failed to regenerate extension imports:", error.message);
      }
    } else {
      console.log(`Extension not found: ${name}`);
    }
    return removed;
  },

  extensionsConfigList: async () => {
    const configManager = new ExtensionsConfigManager();
    const projects = configManager.getProjects();
    console.log("Current extensions:", projects);
    return projects;
  },

  extensionsGenerateImports: async () => {
    try {
      await execAsync("node scripts/generate-extension-imports.js");
      console.log("Generated extension imports");
      return true;
    } catch (error) {
      console.error("Failed to generate extension imports:", error.message);
      return false;
    }
  },
};

export default {
  ...dbTasks,
  ...fileTasks,
  ...extensionConfigTasks,
  writeResults: async (data) => {
    await writeResults(data);
    return null;
  },
};
