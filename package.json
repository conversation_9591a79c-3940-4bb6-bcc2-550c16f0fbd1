{"name": "sdt", "scripts": {"sdt:gui": "npx cypress open", "sdt:headless": "npx cypress run --e2e --spec ./src/app/runSdt.ts", "extensions:scan": "node scripts/update-extensions-config.js scan && node scripts/sync-static-imports.js", "extensions:list": "node scripts/update-extensions-config.js list", "extensions:add": "node scripts/update-extensions-config.js add", "extensions:remove": "node scripts/update-extensions-config.js remove", "extensions:sync": "node scripts/sync-static-imports.js", "prestart": "npm run extensions:sync"}, "dependencies": {"@types/fs-extra": "^11.0.4", "cross-env": "^7.0.3", "cypress": "^14.5.1", "cypress-plugin-tab": "^1.0.5", "cypress-real-events": "^1.14.0", "cypress-wait-until": "^3.0.1", "eslint": "^9.23.0", "exceljs": "^4.4.0", "fs-extra": "^10.0.1", "mongodb": "^5.7.0", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.5.2"}}