#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Utility script to update the extensions configuration
 * Can be run from Node.js environment to dynamically update project structure
 */

const CONFIG_PATH = path.join(__dirname, '../src/config/extensions.json');
const PROJECTS_DIR = path.join(__dirname, '../projects');

/**
 * Scan the projects directory and automatically detect extensions
 */
function scanProjectsDirectory() {
  const projects = [];
  
  if (!fs.existsSync(PROJECTS_DIR)) {
    console.warn('Projects directory not found:', PROJECTS_DIR);
    return projects;
  }

  // Recursively scan for extension/sdt.ts files
  function scanDirectory(dir, relativePath = '') {
    const items = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const item of items) {
      if (item.isDirectory()) {
        const itemPath = path.join(dir, item.name);
        const newRelativePath = relativePath ? `${relativePath}/${item.name}` : item.name;
        
        // Check if this directory has an extension/sdt.ts file
        const extensionPath = path.join(itemPath, 'extension', 'sdt.ts');
        if (fs.existsSync(extensionPath)) {
          projects.push({
            name: item.name,
            path: newRelativePath
          });
        } else {
          // Continue scanning subdirectories
          scanDirectory(itemPath, newRelativePath);
        }
      }
    }
  }

  scanDirectory(PROJECTS_DIR);
  return projects;
}

/**
 * Load current configuration
 */
function loadConfig() {
  try {
    if (fs.existsSync(CONFIG_PATH)) {
      const content = fs.readFileSync(CONFIG_PATH, 'utf8');
      return JSON.parse(content);
    }
  } catch (error) {
    console.warn('Error loading config:', error.message);
  }
  
  return { projects: [] };
}

/**
 * Save configuration
 */
function saveConfig(config) {
  const dir = path.dirname(CONFIG_PATH);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  
  fs.writeFileSync(CONFIG_PATH, JSON.stringify(config, null, 2));
  console.log('Configuration saved to:', CONFIG_PATH);
}

/**
 * Add a new project to the configuration
 */
function addProject(name, projectPath) {
  const config = loadConfig();
  
  // Check if project already exists
  const existingIndex = config.projects.findIndex(p => p.name === name);
  
  if (existingIndex >= 0) {
    config.projects[existingIndex] = { name, path: projectPath };
    console.log(`Updated existing project: ${name} -> ${projectPath}`);
  } else {
    config.projects.push({ name, path: projectPath });
    console.log(`Added new project: ${name} -> ${projectPath}`);
  }
  
  saveConfig(config);
}

/**
 * Remove a project from the configuration
 */
function removeProject(name) {
  const config = loadConfig();
  const initialLength = config.projects.length;
  
  config.projects = config.projects.filter(p => p.name !== name);
  
  if (config.projects.length < initialLength) {
    console.log(`Removed project: ${name}`);
    saveConfig(config);
  } else {
    console.log(`Project not found: ${name}`);
  }
}

/**
 * Auto-scan and update configuration
 */
function autoScan() {
  const scannedProjects = scanProjectsDirectory();
  const config = { projects: scannedProjects };
  
  console.log('Auto-scanned projects:');
  scannedProjects.forEach(project => {
    console.log(`  ${project.name} -> ${project.path}`);
  });
  
  saveConfig(config);
}

/**
 * List current projects
 */
function listProjects() {
  const config = loadConfig();
  
  console.log('Current projects:');
  config.projects.forEach(project => {
    console.log(`  ${project.name} -> ${project.path}`);
  });
}

// Command line interface
const command = process.argv[2];
const arg1 = process.argv[3];
const arg2 = process.argv[4];

switch (command) {
  case 'add':
    if (!arg1 || !arg2) {
      console.error('Usage: node update-extensions-config.js add <name> <path>');
      process.exit(1);
    }
    addProject(arg1, arg2);
    break;
    
  case 'remove':
    if (!arg1) {
      console.error('Usage: node update-extensions-config.js remove <name>');
      process.exit(1);
    }
    removeProject(arg1);
    break;
    
  case 'scan':
    autoScan();
    break;
    
  case 'list':
    listProjects();
    break;
    
  default:
    console.log('Usage:');
    console.log('  node update-extensions-config.js add <name> <path>    - Add/update a project');
    console.log('  node update-extensions-config.js remove <name>       - Remove a project');
    console.log('  node update-extensions-config.js scan               - Auto-scan projects directory');
    console.log('  node update-extensions-config.js list               - List current projects');
    break;
}
