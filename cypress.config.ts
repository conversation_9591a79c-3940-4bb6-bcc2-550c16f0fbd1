import { MongoClient } from "mongodb";
import { defineConfig } from "cypress";
import fs from "fs-extra";
import parseExcelFile from "./src/app/plugins/getTests/parseExcelFile";
import path from "path";
import tasks from "./src/app/tasks";

// Extension scanning and configuration
function scanForExtensions() {
  const extensions: Array<{ name: string; path: string; importPath: string }> =
    [];
  const projectsDir = path.join(__dirname, "projects");

  if (!fs.existsSync(projectsDir)) {
    console.warn("Projects directory not found:", projectsDir);
    return extensions;
  }

  // Recursively scan for extension/sdt.ts files
  function scanDirectory(dir: string, relativePath = "") {
    const items = fs.readdirSync(dir, { withFileTypes: true });

    for (const item of items) {
      if (item.isDirectory()) {
        const itemPath = path.join(dir, item.name);
        const newRelativePath = relativePath
          ? `${relativePath}/${item.name}`
          : item.name;

        // Check if this directory has an extension/sdt.ts file
        const extensionPath = path.join(itemPath, "extension", "sdt.ts");
        if (fs.existsSync(extensionPath)) {
          extensions.push({
            name: item.name,
            path: newRelativePath,
            importPath: `./projects/${newRelativePath}/extension/sdt`,
          });
        } else {
          // Continue scanning subdirectories
          scanDirectory(itemPath, newRelativePath);
        }
      }
    }
  }

  scanDirectory(projectsDir);
  return extensions;
}

// Load extensions configuration
function loadExtensionsConfig() {
  const configPath = path.join(__dirname, "src/config/extensions.json");
  try {
    if (fs.existsSync(configPath)) {
      const content = fs.readFileSync(configPath, "utf8");
      return JSON.parse(content);
    }
  } catch (error) {
    console.warn("Error loading extensions config:", error);
  }

  return { projects: [] };
}

// Generate extension imports map
function generateExtensionImportsMap() {
  const availableExtensions = scanForExtensions();
  const config = loadExtensionsConfig();

  // Create a simple map of all available extensions (app name -> import path)
  const allExtensionsMap: Record<string, string> = {};
  for (const ext of availableExtensions) {
    allExtensionsMap[ext.name] = ext.importPath;
  }

  // Create configured extensions map
  const configuredExtensionsMap: Record<string, string> = {};
  const availableExtensionNames: string[] = Object.keys(allExtensionsMap);

  // Filter based on configuration
  for (const project of config.projects) {
    if (allExtensionsMap[project.name]) {
      configuredExtensionsMap[project.name] = allExtensionsMap[project.name];
    } else {
      console.warn(
        `Extension '${
          project.name
        }' is configured but not found. Available: ${availableExtensionNames.join(
          ", "
        )}`
      );
    }
  }

  console.log("Loaded extensions:", Object.keys(configuredExtensionsMap));
  return {
    extensionImportMap: configuredExtensionsMap,
    allExtensionsMap,
    availableExtensions: availableExtensionNames,
  };
}

export default defineConfig({
  e2e: {
    numTestsKeptInMemory: 1,
    fileServerFolder: "./",
    supportFile: "./src/app/e2e.ts",
    specPattern: "./src/app/runSdt.ts",
    excludeSpecPattern: "",
    trashAssetsBeforeRuns: true,
    video: true,
    chromeWebSecurity: false,
    userAgent:
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.190 Safari/537.36",
    baseUrl: "http://localhost:4200",
    viewportWidth: 1920,
    viewportHeight: 1080,
    defaultCommandTimeout: 10000,
    pageLoadTimeout: 10000,
    retries: {
      runMode: 1,
      openMode: 0,
    },
    env: {
      BROWSER: "chrome",
    },

    async setupNodeEvents(on, config) {
      try {
        getAppConfiguration(config);
        setupPaths(config);

        // Generate and add extension configuration
        const extensionConfig = generateExtensionImportsMap();
        config.env.extensionImportMap = extensionConfig.extensionImportMap;
        config.env.allExtensionsMap = extensionConfig.allExtensionsMap;
        config.env.availableExtensions = extensionConfig.availableExtensions;

        console.log("Configuration loaded:", config.env);
      } catch (error) {
        console.error("Failed to set configuration:", error);
        throw error;
      }

      on("task", await getTasks());

      on("after:screenshot", (details) => {
        return new Promise((resolve) => {
          if (!fs.existsSync(config.env.resultsScreenshotsTempFolder)) {
            fs.mkdirSync(config.env.resultsScreenshotsTempFolder);
          }
          let screenshotPath;
          if (details.testFailure || details.name?.match(/error*.*/)) {
            screenshotPath = `${
              config.env.resultsScreenshotsTempFolder
            }/error-${Date.now()}.png`;
          } else {
            screenshotPath = `${config.env.resultsScreenshotsTempFolder}/${details.name}`;
          }
          fs.moveSync(details.path, screenshotPath, { overwrite: true });
          resolve({ path: screenshotPath });
        });
      });

      await readSdtWorkbookFile(config);

      setWatchers(config);

      return config;
    },
  },
});

interface ConfigEnvironment {
  runFolder?: string;
  configFilePath?: string;
  app?: string;
  sdtFile?: string;
  resultsFolderName?: string;
  resultsFolderPath?: string;
  resultsScreenshotsTempFolder?: string;
  [key: string]: any;
}

interface Config {
  env: ConfigEnvironment;
}

function loadJsonFile(filePath: string): object {
  try {
    const buffer = fs.readFileSync(filePath);
    const jsonFileContent = JSON.parse(buffer.toString());
    return jsonFileContent;
  } catch (error) {
    console.error(`Failed to load JSON file ${filePath}:`, error);
    return {};
  }
}

function getAppConfiguration(config: Config): void {
  const runFolder = process.env.RUN_FOLDER;
  const configPath = process.env.CONFIG_PATH;

  if (!runFolder || !configPath) {
    throw new Error(
      "Required environment variables RUN_FOLDER or CONFIG_PATH are missing"
    );
  }

  const appConfig = loadJsonFile(configPath);

  config.env = {
    ...config.env,
    runFolder,
    configFilePath: configPath,
    ...appConfig,
  };

  if (!config.env.app) {
    config.env.app = process.env.APP;
  }
}

function setupPaths(config: Config): void {
  const { runFolder, app, sdtFile, resultsFolderName } = config.env;

  if (!runFolder) {
    throw new Error("runFolder is not defined in configuration");
  }

  config.env.sdtFilePath = path.resolve(runFolder, sdtFile ?? `${app}.xlsx`);

  config.env.resultsFolderPath = path.resolve(
    runFolder,
    resultsFolderName ?? "results"
  );

  config.env.resultsScreenshotsTempFolder = path.resolve(
    config.env.resultsFolderPath,
    "temp"
  );
}

async function getTasks() {
  let db;
  if (process.env.RSD_DB_URL) {
    const dbUrl = process.env.RSD_DB_URL;
    const mongoClient = await MongoClient.connect(dbUrl as string);
    db = mongoClient.db();
  }
  global.db = db;
  return await tasks;
}

async function readSdtWorkbookFile(config) {
  await parseExcelFile(config.env.sdtFilePath).then((testsData) => {
    config.env.sdtData = { ...testsData };
  });
}

function setWatchers(config) {
  fs.watchFile(
    config.env.configFilePath,
    {
      persistent: true,
      interval: 1000,
    },
    () => {
      fs.utimesSync("./cypress.config.ts", new Date(), new Date());
    }
  );
  fs.watchFile(
    config.env.sdtFilePath,
    {
      persistent: true,
      interval: 1000,
    },
    () => {
      fs.utimesSync("./cypress.config.ts", new Date(), new Date());
    }
  );
}
