#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Build-time script to generate static extension imports
 * This solves the dynamic import issue by generating static imports at build time
 */

const PROJECTS_DIR = path.join(__dirname, '../projects');
const OUTPUT_FILE = path.join(__dirname, '../src/app/generated-extension-imports.ts');

/**
 * Scan the projects directory and find all extensions
 */
function scanForExtensions() {
  const extensions = [];
  
  if (!fs.existsSync(PROJECTS_DIR)) {
    console.warn('Projects directory not found:', PROJECTS_DIR);
    return extensions;
  }

  // Recursively scan for extension/sdt.ts files
  function scanDirectory(dir, relativePath = '') {
    const items = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const item of items) {
      if (item.isDirectory()) {
        const itemPath = path.join(dir, item.name);
        const newRelativePath = relativePath ? `${relativePath}/${item.name}` : item.name;
        
        // Check if this directory has an extension/sdt.ts file
        const extensionPath = path.join(itemPath, 'extension', 'sdt.ts');
        if (fs.existsSync(extensionPath)) {
          extensions.push({
            name: item.name,
            path: newRelativePath,
            importPath: `../../projects/${newRelativePath}/extension/sdt`
          });
        } else {
          // Continue scanning subdirectories
          scanDirectory(itemPath, newRelativePath);
        }
      }
    }
  }

  scanDirectory(PROJECTS_DIR);
  return extensions;
}

/**
 * Generate TypeScript file with static imports
 */
function generateImportsFile(extensions) {
  const imports = extensions.map(ext => 
    `  "${ext.name}": () => import("${ext.importPath}"),`
  ).join('\n');

  const content = `// This file is auto-generated by scripts/generate-extension-imports.js
// Do not edit manually - it will be overwritten

/**
 * Static extension import map
 * Generated at build time to ensure all imports are statically analyzable
 */
export const extensionImportMap: Record<string, () => Promise<any>> = {
${imports}
};

/**
 * List of all available extensions
 */
export const availableExtensions = [
${extensions.map(ext => `  "${ext.name}",`).join('\n')}
];

/**
 * Extension metadata
 */
export const extensionMetadata = {
${extensions.map(ext => `  "${ext.name}": { name: "${ext.name}", path: "${ext.path}" },`).join('\n')}
};
`;

  // Ensure output directory exists
  const outputDir = path.dirname(OUTPUT_FILE);
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  fs.writeFileSync(OUTPUT_FILE, content);
  console.log(`Generated extension imports file: ${OUTPUT_FILE}`);
  console.log(`Found ${extensions.length} extensions:`);
  extensions.forEach(ext => {
    console.log(`  ${ext.name} -> ${ext.path}`);
  });
}

/**
 * Main function
 */
function main() {
  console.log('Scanning for extensions...');
  const extensions = scanForExtensions();
  
  if (extensions.length === 0) {
    console.warn('No extensions found!');
  }
  
  generateImportsFile(extensions);
  console.log('Extension imports generation complete.');
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { scanForExtensions, generateImportsFile, main };
