#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * <PERSON>ript to sync static imports in sdt.ts with discovered extensions
 * This ensures webpack can bundle all extensions while maintaining dynamic configuration
 */

const PROJECTS_DIR = path.join(__dirname, '../projects');
const SDT_FILE = path.join(__dirname, '../src/app/sdt.ts');

/**
 * Scan for extensions
 */
function scanForExtensions() {
  const extensions = [];
  
  if (!fs.existsSync(PROJECTS_DIR)) {
    console.warn('Projects directory not found:', PROJECTS_DIR);
    return extensions;
  }

  function scanDirectory(dir, relativePath = '') {
    const items = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const item of items) {
      if (item.isDirectory()) {
        const itemPath = path.join(dir, item.name);
        const newRelativePath = relativePath ? `${relativePath}/${item.name}` : item.name;
        
        const extensionPath = path.join(itemPath, 'extension', 'sdt.ts');
        if (fs.existsSync(extensionPath)) {
          extensions.push({
            name: item.name,
            path: newRelativePath,
            importPath: `../../projects/${newRelativePath}/extension/sdt`
          });
        } else {
          scanDirectory(itemPath, newRelativePath);
        }
      }
    }
  }

  scanDirectory(PROJECTS_DIR);
  return extensions;
}

/**
 * Update the static import map in sdt.ts
 */
function updateStaticImports(extensions) {
  if (!fs.existsSync(SDT_FILE)) {
    console.error('SDT file not found:', SDT_FILE);
    return false;
  }

  let content = fs.readFileSync(SDT_FILE, 'utf8');
  
  // Generate the static import map
  const imports = extensions.map(ext => 
    `      ${ext.name}: () => import("${ext.importPath}"),`
  ).join('\n');

  const staticImportMapRegex = /(\/\/ Static import map that webpack can analyze\s*const staticImportMap: Record<string, \(\) => Promise<any>> = \{)([\s\S]*?)(\s*\};)/;
  
  const newStaticImportMap = `// Static import map that webpack can analyze
    const staticImportMap: Record<string, () => Promise<any>> = {
${imports}
    };`;

  if (staticImportMapRegex.test(content)) {
    content = content.replace(staticImportMapRegex, newStaticImportMap);
    fs.writeFileSync(SDT_FILE, content);
    console.log('Updated static imports in sdt.ts');
    console.log(`Found ${extensions.length} extensions:`);
    extensions.forEach(ext => {
      console.log(`  ${ext.name} -> ${ext.path}`);
    });
    return true;
  } else {
    console.error('Could not find static import map pattern in sdt.ts');
    return false;
  }
}

/**
 * Main function
 */
function main() {
  console.log('Scanning for extensions...');
  const extensions = scanForExtensions();
  
  if (extensions.length === 0) {
    console.warn('No extensions found!');
    return;
  }
  
  if (updateStaticImports(extensions)) {
    console.log('Static imports sync complete.');
  } else {
    console.error('Failed to sync static imports.');
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { scanForExtensions, updateStaticImports, main };
