// This file is auto-generated by scripts/generate-extension-imports.js
// Do not edit manually - it will be overwritten

/**
 * Static extension import map
 * Generated at build time to ensure all imports are statically analyzable
 */
export const extensionImportMap: Record<string, () => Promise<any>> = {
  "jba": () => import("../../projects/dorian solutions/jba/extension/sdt"),
  "rwa": () => import("../../projects/other/rwa/extension/sdt"),
  "billing": () => import("../../projects/riverstar/billing/extension/sdt"),
  "ce": () => import("../../projects/riverstar/ce/extension/sdt"),
  "pwv": () => import("../../projects/riverstar/pwv/extension/sdt"),
  "riverstar": () => import("../../projects/riverstar/riverstar/extension/sdt"),
  "srm": () => import("../../projects/riverstar/srm/extension/sdt"),
};

/**
 * List of all available extensions
 */
export const availableExtensions = [
  "jba",
  "rwa",
  "billing",
  "ce",
  "pwv",
  "riverstar",
  "srm",
];

/**
 * Extension metadata
 */
export const extensionMetadata = {
  "jba": { name: "jba", path: "dorian solutions/jba" },
  "rwa": { name: "rwa", path: "other/rwa" },
  "billing": { name: "billing", path: "riverstar/billing" },
  "ce": { name: "ce", path: "riverstar/ce" },
  "pwv": { name: "pwv", path: "riverstar/pwv" },
  "riverstar": { name: "riverstar", path: "riverstar/riverstar" },
  "srm": { name: "srm", path: "riverstar/srm" },
};
