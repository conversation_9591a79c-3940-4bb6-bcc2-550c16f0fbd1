import Office from "../domain/office";
import Patient from "../domain/patient";
import Provider from "../domain/provider";
import User from "../../../../riverstar/.organization/domain/user";
import cypressHelper from "@/app/helpers/cypress";
import h from "@/app/helpers/all";

export default {
  "Check Stepper": function () {
    Cypress.sdt.extension.stepperHandler.checkStepper(
      Cypress.sdt.current.step.simpleValues
    );
  },
  "Create Office": function (
    officeData = Cypress.sdt.current.step.simpleValues[0]
  ) {
    cypressHelper.logTitle("Create Office", h.titleLevel0).then(() => {
      const office = new Office(officeData);
      Cypress.sdt.domain.data.office = office;
      cy.task("dbReadOffice", office.name).then((office) => {
        if (!office) {
          Cypress.sdt.domain.data.office.create();
        }
      });
    });
  },
  "Create Offices": function () {
    const offices = Cypress.sdt.current.step.simpleValues;
    cypressHelper.logTitle("Create Offices", h.titleLevel0).then(() => {
      offices.forEach((officeData) => {
        cy.then(() => {
          const office = new Office(officeData);
          if (!Cypress.sdt.domain.data.offices)
            Cypress.sdt.domain.data.offices = [];
          Cypress.sdt.domain.data.offices.push(office);
          office.create();
        });
      });
    });
  },
  "Create Patient": function (
    patientData = Cypress.sdt.current.step.simpleValues[0]
  ) {
    cypressHelper.logTitle("Create Patient", h.titleLevel0).then(() => {
      Cypress.sdt.domain.data.patient = new Patient(patientData, true);
      Cypress.sdt.domain.data.patient.create();
    });
  },
  "Create Patients": function () {
    const patients = Cypress.sdt.current.step.simpleValues;
    patients.forEach((patientData, index) => {
      cy.then(() => {
        const patient = new Patient(patientData);
        Cypress.sdt.domain.data[`patient${index + 1}`] = patient;
        patient.create();
      });
    });
  },
  "Create Provider": function (
    providerData = Cypress.sdt.current.step.simpleValues[0]
  ) {
    cypressHelper.logTitle("Create Provider", h.titleLevel0).then(() => {
      Cypress.sdt.domain.data.provider = new Provider(providerData);
      Cypress.sdt.domain.data.provider.create();
    });
  },
  "Set Patient Provider": function () {
    cypressHelper
      .logTitle("Set Patient Provider", h.titleLevel0)
      .then(() =>
        Cypress.sdt.apiHandler.setPatientProvider(
          Cypress.sdt.domain.data.provider.id
        )
      );
  },
  "Create Clinician": function (
    clinicianData = Cypress.sdt.current.step.simpleValues[0]
  ) {
    cypressHelper.logTitle("Create Clinician", h.titleLevel0).then(() => {
      Cypress.sdt.domain.data.clinician = new User(clinicianData);
      Cypress.sdt.domain.data.clinician.create();
    });
  },
  "Login Clinician": function () {
    cypressHelper
      .logTitle("Login Clinician", h.titleLevel0)
      .then(() =>
        Cypress.sdt.apiHandler.login(
          Cypress.sdt.domain.data.clinician.username,
          Cypress.sdt.domain.data.clinician.password
        )
      );
  },
  "Login Provider": function () {
    cypressHelper
      .logTitle("Login Provider", h.titleLevel0)
      .then(() =>
        Cypress.sdt.apiHandler.login(
          Cypress.sdt.domain.data.provider.username,
          Cypress.sdt.domain.data.provider.password
        )
      );
  },
  "Select Provider": function () {
    cypressHelper.logTitle("Select Provider", h.titleLevel0).then(() => {
      if (Cypress.sdt.domain.data.provider) {
        Cypress.sdt.apiHandler.selectProvider(
          Cypress.sdt.domain.data.provider.id
        );
      }
    });
  },
  "Create BH Series": function () {
    let interviewCode;
    cypressHelper
      .logTitle("Create BhSeries", h.titleLevel0)
      .then(() => {
        const bhScreenEncounter = {
          appointmentStatus: "Pending",
          bypassSchedule: false,
          createdById: null,
          currentStatus: "Started",
          currentStatusDate: null,
          currentStatusMsg: "",
          encounterType: "bhScreenMINI",
          interactionDuration: 0,
          isActive: true,
          languagePreference: "en",
          locationId: Cypress.sdt.domain.data.office.id,
          office: {
            officeDescription: "",
            address: "",
            address2: "",
            city: "",
            state: "",
            zip: "",
          },
          patientId: Cypress.sdt.domain.data.patient.data.id,
          physioSurveyId: null,
          pos: {
            administrationOption: "practiceStaff",
            email: "",
            phone: "",
            selfAdminCode: "",
            selfAdminUrl: "",
            notifyInterviewer: false,
            telehealth: false,
          },
          providerUserId: Cypress.sdt.domain.data.provider.id,
          scheduleDate: null,
          soapNotes: null,
          statusHistory: [],
          title: "",
          updatedById: null,
          zoom: {
            startUrl: "",
            joinUrl: "",
            id: "",
          },
          review: {},
        };
        Cypress.sdt.apiHandler.createBhSeries(bhScreenEncounter);
      })
      .then(() => cypressHelper.delay(4))
      .then(() =>
        Cypress.sdt.dbHandler.readLastEncounterWithType("bhScreenMINI")
      )
      .then((encounter) => (interviewCode = encounter["pos"].interviewCode))
      .then(() => Cypress.sdt.apiHandler.startScreen(interviewCode));
  },
  "Run Screen": function () {
    let posInterviewId, sdsInterviewTestId;
    cypressHelper.logTitle("Run Screen", h.titleLevel0).then(() => {
      cypressHelper
        .delay(4)
        .then(() =>
          Cypress.sdt.dbHandler.readLastEncounterWithType("bhScreenMINI")
        )
        .then((encounter) => {
          posInterviewId = encounter["pos"]["interviewTestId"];
          sdsInterviewTestId = encounter["sds"]["interviewTestId"];
        })
        .then(() => {
          const screen = {
            answersRequest: [
              {
                action: null,
                answers: [
                  {
                    questionId: 228119,
                    answer: {
                      value: "no",
                    },
                  },
                  {
                    questionId: 228120,
                    answer: {
                      value: "no",
                    },
                  },
                  {
                    questionId: 228121,
                    answer: {
                      value: "no",
                    },
                  },
                  {
                    questionId: 228122,
                    answer: {
                      value: "no",
                    },
                  },
                  {
                    questionId: 228123,
                    answer: {
                      value: "no",
                    },
                  },
                  {
                    questionId: 228124,
                    answer: {
                      value: "no",
                    },
                  },
                  {
                    questionId: 228125,
                    answer: {
                      value: "no",
                    },
                  },
                  {
                    questionId: 228126,
                    answer: {
                      value: "no",
                    },
                  },
                  {
                    questionId: 228127,
                    answer: {
                      value: "no",
                    },
                  },
                  {
                    questionId: 228128,
                    answer: {
                      value: "no",
                    },
                  },
                  {
                    questionId: 228129,
                    answer: {
                      value: "no",
                    },
                  },
                  {
                    questionId: 228130,
                    answer: {
                      value: "no",
                    },
                  },
                  {
                    questionId: 228131,
                    answer: {
                      value: "no",
                    },
                  },
                  {
                    questionId: 228132,
                    answer: {
                      value: "no",
                    },
                  },
                  {
                    questionId: 228133,
                    answer: {
                      value: "no",
                    },
                  },
                  {
                    questionId: 228134,
                    answer: {
                      value: "no",
                    },
                  },
                  {
                    questionId: 228135,
                    answer: {
                      value: "no",
                    },
                  },
                  {
                    questionId: 228136,
                    answer: {
                      value: "no",
                    },
                  },
                  {
                    questionId: 228138,
                    answer: {
                      value: "no",
                    },
                  },
                  {
                    questionId: 228139,
                    answer: {
                      value: "no",
                    },
                  },
                  {
                    questionId: 228137,
                    answer: {
                      value: "no",
                    },
                  },
                ],
                clinicianNotes: "",
                interviewTestId: posInterviewId,
              },
              {
                action: null,
                answers: [
                  {
                    questionId: 1590,
                    answer: {
                      value: 0,
                    },
                  },
                  {
                    questionId: 1591,
                    answer: {
                      value: 0,
                    },
                  },
                  {
                    questionId: 1592,
                    answer: {
                      value: 0,
                    },
                  },
                  {
                    questionId: 1593,
                    answer: {
                      value: "0",
                    },
                  },
                  {
                    questionId: 1594,
                    answer: {
                      value: "0",
                    },
                  },
                ],
                clinicianNotes: "",
                interviewTestId: sdsInterviewTestId,
              },
            ],
          };
          Cypress.sdt.apiHandler.postScreen(screen);
        });
    });
  },
  "Run Review Screen": function () {
    let interviewTestId;
    cypressHelper
      .logTitle("Run Review Screen", h.titleLevel0)
      .then(() =>
        Cypress.sdt.dbHandler.readLastEncounterWithType("bhScreenMINI")
      )
      .then(
        (screenEncounter) =>
          (interviewTestId = screenEncounter["pos"]["interviewTestId"])
      )
      .then(() => {
        Cypress.sdt.dbHandler
          .readLastEncounterWithType("bhReviewScreen")
          .then((encounter: object) => {
            encounter = {
              ...encounter,
              ...{
                dsmDisorders: true,
                currentStatus: "Completed",
                review: {
                  reportComplete: true,
                  reportCompleteAt: encounter["currentStatusDate"],
                  reportId: interviewTestId,
                  reportInterviewTestId: interviewTestId,
                  dsmDisorders: true,
                  nextSteps: "miniInterview",
                },
              },
            };
            Cypress.sdt.apiHandler.reviewScreen(encounter);
          });
      });
  },
  "Run Interview": function () {
    let interviewCode;
    let posInterviewTestId;
    let encounter;

    const sendInterviewAnswer = (answerResponse) => {
      if (answerResponse.proemInterviewResponse[0].completed) {
        return;
      }
      let questionId;
      let answer;
      const itemType =
        answerResponse.proemInterviewResponse[0].interview.modules[0]
          .sequenceOrders[0].itemType;
      if (itemType === "question") {
        questionId =
          answerResponse.proemInterviewResponse[0].interview.modules[0]
            .sequenceOrders[0].questionId;
        const questionAnswerType =
          answerResponse.proemInterviewResponse[0].interview.modules[0]
            .sequenceOrders[0].question.questionAnswerType;
        answer = {
          answersRequest: [
            {
              answers: [
                {
                  questionId: questionId,
                  answer: {
                    value: "no",
                  },
                },
              ],
              clinicianNotes: "",
              interviewTestId: posInterviewTestId,
              action: null,
            },
          ],
        };
        if (questionAnswerType === "time") {
          answer.answersRequest[0].answers[0].answer.value = "0:0";
        }
        if (questionAnswerType === "number") {
          answer.answersRequest[0].answers[0].answer.value = "0";
        }
      }
      Cypress.sdt.apiHandler
        .sendInterviewAnswer(answer)
        .then((response) => sendInterviewAnswer(response));
    };

    cypressHelper
      .logTitle("Run Interview", h.titleLevel0)
      .then(() => cy.task("dbReadBhSeries"))
      .then((bhSeries) => {
        encounter = {
          appointmentStatus: "Pending",
          bhSeriesId: bhSeries["id"],
          bypassSchedule: false,
          createdById: null,
          currentStatus: "Started",
          currentStatusDate: null,
          currentStatusMsg: "",
          encounterType: "bhInterview",
          interactionDuration: 0,
          isActive: true,
          languagePreference: "en",
          locationId: Cypress.sdt.domain.data.office.id,
          office: {
            officeDescription: "",
            address: "",
            address2: "",
            city: "",
            state: "",
            zip: "",
          },
          patientId: Cypress.sdt.domain.data.patient.data.id,
          physioSurveyId: null,
          pos: {
            administrationOption: "practiceStaff",
            email: "",
            phone: "",
            selfAdminCode: "",
            selfAdminUrl: "",
            notifyInterviewer: false,
            telehealth: false,
          },
          providerUserId: Cypress.sdt.domain.data.provider.id,
          review: {},
          scheduleDate: null,
          soapNotes: null,
          statusHistory: [],
          title: "",
          updatedById: null,
          zoom: {
            startUrl: "",
            joinUrl: "",
            id: "",
          },
        };
        Cypress.sdt.apiHandler.createEncounter(encounter);
      })
      .then(() =>
        Cypress.sdt.dbHandler.readLastEncounterWithType("bhInterview")
      )
      .then((encounter) => {
        interviewCode = encounter["pos"].interviewCode;
        posInterviewTestId = encounter["pos"]["interviewTestId"];
      })
      .then(() => {
        Cypress.sdt.apiHandler.startInterview(interviewCode);
      })
      .then((startInterviewResponse) => {
        sendInterviewAnswer(startInterviewResponse);
      });
  },
  "Run Review Interview": function () {
    let encounter;
    let posInterviewTestId;
    cypressHelper
      .logTitle("Run Review Interview", h.titleLevel0)
      .then(() =>
        Cypress.sdt.dbHandler.readLastEncounterWithType("bhScreenMINI")
      )
      .then(
        (screenEncounter) =>
          (posInterviewTestId = screenEncounter["pos"]["interviewTestId"])
      )
      .then(() =>
        Cypress.sdt.dbHandler.readLastEncounterWithType("bhReviewInterview")
      )
      .then(() => {
        encounter = {
          ...encounter,
          ...{
            review: {
              diagnosisIndex: [
                {
                  seq: 0,
                  index: 0,
                },
              ],
              diagnosisCodes: ["Z03.89"],
              diagnosisDescriptions: [
                "Z03.89 Encounter for observation for other suspected diseases and conditions ruled out",
              ],
              reportComplete: true,
              reportCompleteAt: encounter["currentStatusDate"],
              reportId: posInterviewTestId,
              reportInterviewTestId: posInterviewTestId,
              doesNotMeetCriteria: true,
            },
            currentStatus: "Completed",
          },
        };
      })
      .then(() => Cypress.sdt.apiHandler.updateEncounter(encounter));
  },
  "Goto Screen": function () {
    cy.wrap(() => this["Set Data For BH Series"]())
      .then(() => this["Login Clinician"]())
      .then(() => this["Select Provider"]());
  },
  "Goto Review Screen": function () {
    cy.wrap(this["Goto Screen"]())
      .then(() => this["Create BH Series"]())
      .then(() => this["Run Screen"]())
      .then(() => this["Login Provider"]())
      .then(() => this["Select Provider"]());
  },
  "Goto Interview": function () {
    cy.wrap(this["Goto Review Screen"]())
      .then(() => this["Run Review Screen"]())
      .then(() => this["Login Clinician"]())
      .then(() => this["Select Provider"]());
  },
  "Goto Review Interview": function () {
    cy.wrap(this["Goto Interview"]())
      .then(() => this["Run Interview"]())
      .then(() => this["Login Provider"]())
      .then(() => this["Select Provider"]());
  },
  "Complete BH Series": function () {
    cy.wrap(this["Goto Review Interview"]()).then(() =>
      this["Run Review Interview"]()
    );
  },
  "Set Scale Value": function () {
    const area = Cypress.sdt.current.step.simpleValues[0];
    const scaleValue = parseInt(Cypress.sdt.current.step.simpleValues[1]);
    cy.get(`.questionForm:contains(${area}) [name=sds-scale-radio-button]`)
      .eq(scaleValue)
      .click();
  },
  "Set Data For BH Series": function () {
    const office = Cypress.sdt.current.step.simpleValues[0];
    const clinician = Cypress.sdt.current.step.simpleValues[1];
    const patient = Cypress.sdt.current.step.simpleValues[2];

    cy.wrap(this["Create Office"](office))
      .then(() => {
        this["Create Clinician"](clinician);
      })
      .then(() => {
        this["Create Patient"](patient);
      })
      .then(() => {
        if (clinician.Provider) this["Create Provider"](clinician.Provider);
      })
      .then(() => {
        Cypress.sdt.apiHandler.setCurrentPatient(
          Cypress.sdt.domain.data.patient
        );
      });
  },
  "Create Monitor": function () {
    const monitorType = Cypress.sdt.current.step.simpleValues[0];
    const frequency = Cypress.sdt.current.step.simpleValues[1].toLowerCase();
    const startDate = Cypress.sdt.current.step.simpleValues[2];
    const endDate = Cypress.sdt.current.step.simpleValues[3];

    cy.task("dbReadMonitorType", monitorType)
      .then((monitorType) => {
        return {
          monitorTypeId: monitorType["id"],
          patientId: Cypress.sdt.domain.data.patient.data.id,
          lastMonitorReviewId: null,
          locationId: Cypress.sdt.domain.data.office.id,
          bhSeriesId: Cypress.sdt.domain.bhSeries?.data.id,
          frequency: frequency,
          startDate: startDate,
          endDate: endDate,
          currentStatus: "notStarted",
          currentStatusDate: h.getLocalDateAndTime(),
          providerUserId: Cypress.sdt.domain.data.provider.id,
          administratorId: null,
          languagePreference: "en",
          substanceTypes: [],
          screenAdministrationOption: "email",
          screenAdministrationContactEmail:
            Cypress.sdt.domain.data.patient.data.email,
          bhSeriesDate: Cypress.sdt.domain.bhSeries?.data.createdAt,
        };
      })
      .then((monitor) => Cypress.sdt.apiHandler.createMonitor(monitor));
  },
  "Create Monitor Package": function () {
    const packageDate = Cypress.sdt.current.step.simpleValues[0];
    Cypress.sdt.apiHandler.createMonitorPackage(packageDate);
  },
  "Open Monitor Event Form": function () {
    const todayDate = Cypress.sdt.current.step.simpleValues[0];
    cy.task("dbReadMonitorPackage").then((monitorPackage) => {
      const url = `${monitorPackage["selfAdminUrl"]}&code=${monitorPackage["selfAdminCode"]}&today=${todayDate}`;
      cy.visit(url);
    });
  },
  "Fill Question": function () {
    const mainQuestion = Cypress.sdt.current.step.simpleValues[0];
    const option = Cypress.sdt.current.step.simpleValues[1];
    let $mainQuestionElement;
    cy.contains(mainQuestion)
      .parents("form:first")
      .find(".checkmark")
      .filter((_index, element) => element.innerText.trim() === option)
      .click({ force: true })
      .then(($el) => {
        $mainQuestionElement = $el;
        if (
          Cypress.sdt.current.step.simpleValues[2] &&
          /^\d+$/.test(Cypress.sdt.current.step.simpleValues[2])
        ) {
          const value = Cypress.sdt.current.step.simpleValues[2];
          cy.wrap($el).closest("form").find("form input").clear().type(value);
        }
      })
      .then(() => {
        if (
          Cypress.sdt.current.step.simpleValues[2] &&
          !/^\d+$/.test(Cypress.sdt.current.step.simpleValues[2])
        ) {
          const additionalOption = Cypress.sdt.current.step.simpleValues[2];
          cy.get($mainQuestionElement)
            .closest("form")
            .find(`form mat-checkbox:contains(${additionalOption})`)
            .filter((_index, element) => {
              return element.innerText.trim() === additionalOption;
            })
            .click();
        }
      })
      .then(() => {
        if (
          Cypress.sdt.current.step.simpleValues[3] &&
          !/^\d+$/.test(Cypress.sdt.current.step.simpleValues[3])
        ) {
          const additionalOption = Cypress.sdt.current.step.simpleValues[3];
          cy.get($mainQuestionElement)
            .closest("form")
            .find(`form mat-checkbox:contains(${additionalOption})`)
            .filter((_index, element) => {
              return element.innerText.trim() === additionalOption;
            })
            .click();
        }
      });
  },
  "Fill Complex Question": function () {
    const mainQuestion = Cypress.sdt.current.step.simpleValues[0];
    const option = Cypress.sdt.current.step.simpleValues[1];
    cy.contains(mainQuestion)
      .parents("form:first")
      .find("input[type='checkbox']")
      .filter(
        (_index, element) => (element as HTMLInputElement).value === option
      )
      .click({ force: true });
  },
};
