const fs = require('fs');
const path = require('path');

const PROJECTS_DIR = path.join(__dirname, '../projects');
const SDT_FILE = path.join(__dirname, '../src/app/sdt.ts');

function scanForExtensions() {
  const extensions = [];
  
  function scanDirectory(dir, relativePath = '') {
    const items = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const item of items) {
      if (item.isDirectory()) {
        const itemPath = path.join(dir, item.name);
        const newRelativePath = relativePath ? `${relativePath}/${item.name}` : item.name;
        
        const extensionPath = path.join(itemPath, 'extension', 'sdt.ts');
        if (fs.existsSync(extensionPath)) {
          extensions.push({
            name: item.name,
            path: newRelativePath
          });
        } else {
          scanDirectory(itemPath, newRelativePath);
        }
      }
    }
  }

  if (fs.existsSync(PROJECTS_DIR)) {
    scanDirectory(PROJECTS_DIR);
  }
  
  return extensions;
}

function updateStaticImports() {
  const extensions = scanForExtensions();
  let content = fs.readFileSync(SDT_FILE, 'utf8');
  
  const imports = extensions.map(ext => 
    `      ${ext.name}: () => import("../../projects/${ext.path}/extension/sdt"),`
  ).join('\n');

  const newStaticImportMap = `    const staticImportMap: Record<string, () => Promise<any>> = {
${imports}
    };`;

  const regex = /const staticImportMap: Record<string, \(\) => Promise<any>> = \{[\s\S]*?\};/;
  
  if (regex.test(content)) {
    content = content.replace(regex, newStaticImportMap);
    fs.writeFileSync(SDT_FILE, content);
    console.log(`Updated static imports with ${extensions.length} extensions`);
  } else {
    console.error('Could not find static import map in sdt.ts');
  }
}

updateStaticImports();
