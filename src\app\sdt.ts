import Domain from "@/app/core/domain";
import Report from "@/app/core/report";
import Test from "@/app/core/test";
import coreActions from "@/app/core/actions";
import corePrimitives from "@/app/core/primitives";

export default class Sdt {
  private static getExtensionMap() {
    // Get extension configuration from Cypress environment
    const extensionImportMap = Cypress.env("extensionImportMap") || {};
    const availableExtensions = Cypress.env("availableExtensions") || [];

    // Build extension map with dynamic imports
    const extensionMap: Record<string, () => Promise<any>> = {};

    for (const [extensionName, importPath] of Object.entries(
      extensionImportMap
    )) {
      extensionMap[extensionName] = () => import(importPath as string);
    }

    if (Object.keys(extensionMap).length === 0) {
      console.warn(
        "No extensions configured. Available extensions:",
        availableExtensions
      );
    }

    return extensionMap;
  }

  extension;
  config;
  setup;
  icons;
  actions;
  primitives;
  apiHandler;
  spyHandler;
  apiInterceptors;
  data = {
    tests: [],
    scripts: [],
    tables: [],
    aliases: {},
    elements: {},
  };
  testsToRun;
  domain;
  current = {
    sheet: "",
    test: {},
    step: {},
    stepIndex: -1,
  };
  results = {
    executedTests: [],
    testsWithError: [],
    notCompletedTests: [],
    omittedTests: [],
    omittedSteps: [],
    usedAliases: [],
    usedElements: [],
  };
  report;

  constructor() {
    this.config = {
      ...Cypress.env(),
    };
  }

  private async loadExtension() {
    const projectName = Cypress.env().app;
    const extensionMap = Sdt.getExtensionMap();
    const loader = extensionMap[projectName];
    const extension = await loader();
    return extension.default;
  }

  async initialize() {
    this.extension = await this.loadExtension();

    this.config = {
      ...this.config,
      ...this.extension?.config,
    };

    this.setup = this.extension?.setup;
    this.icons = this.extension?.icons;
    this.actions = {
      ...coreActions,
      ...this.extension?.actions,
    };
    this.primitives = {
      ...corePrimitives,
      ...this.extension?.primitives,
    };
    this.apiHandler = this.extension?.["apiHandler"];
    this.spyHandler = this.extension?.["spyHandler"];
    this.apiInterceptors = this.extension?.["apiInterceptors"];

    this.data.tests = structuredClone(this.config.sdtData.tests);
    this.data.scripts = structuredClone(this.config.sdtData.scripts);
    this.data.tables = structuredClone(this.config.sdtData.tables);
    this.data.aliases = structuredClone(this.config.sdtData.aliases);
    this.data.elements = structuredClone(this.config.sdtData.elements);

    this.report = new Report(this.config);

    this.setTests();

    return this;
  }

  setTests() {
    this.data.tests.forEach((test) => {
      if (test["runFlag"] === "all") {
        this.data.tests
          .filter((t) => t["sheet"] === test["sheet"])
          .forEach((t) => (t["runFlag"] = "z"));
      }
      if (test["runFlag"] === "none") {
        this.data.tests
          .filter((t) => t["sheet"] === test["sheet"])
          .forEach((t) => (t["runFlag"] = null));
      }
    });
    const zPriorityTests = this.data.tests.filter(
      (test) => test["runFlag"] === "z"
    );
    const yPriorityTests = this.data.tests.filter(
      (test) => test["runFlag"] === "y"
    );
    const xPriorityTests = this.data.tests.filter(
      (test) => test["runFlag"] === "x"
    );
    const omittedTests = this.data.tests.filter((test) => !test["runFlag"]);
    if (zPriorityTests.length) {
      this.testsToRun = [...zPriorityTests];
      this.results.omittedTests = [
        ...xPriorityTests,
        ...yPriorityTests,
        ...omittedTests,
      ];
    } else if (yPriorityTests.length) {
      this.testsToRun = [...yPriorityTests, ...zPriorityTests];
      this.results.omittedTests = [...xPriorityTests, ...omittedTests];
    } else {
      this.testsToRun = [...xPriorityTests];
      this.results.omittedTests = omittedTests;
    }
  }

  resetDomain() {
    const entities = Object.values(this.data.tables).reduce((acc, table) => {
      return {
        ...(acc as object),
        ...(table as object),
      };
    }, {});
    this.domain = new Domain(entities);
  }

  run() {
    const sheets = [...new Set(this.testsToRun.map((test) => test.sheet))];
    sheets.forEach((sheetName: string) => {
      const sheetTests = this.testsToRun.filter(
        (test) => test.sheet === sheetName
      );
      describe(sheetName, () => {
        sheetTests.forEach((testData) => {
          const test = new Test(testData);
          test.run();
        });
      });
    });
  }
}
